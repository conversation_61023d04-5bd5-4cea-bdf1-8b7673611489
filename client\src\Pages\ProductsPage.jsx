import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import Products from '../Utils/Products/Products';
import SearchBar from '../Components/SearchBar';
import useProducts from '../Utils/Products/useProducts';
import { useCart } from '../Context/useCart';
import { searchProductByName, groupProductsByCategory } from '../Utils/data/data';

const ProductsPage = () => {
    const navigate = useNavigate();
    const { getCartCount } = useCart();
    const { products, error } = useProducts();
    const [filteredProducts, setFilteredProducts] = useState([]);
    const [isSearching, setIsSearching] = useState(false);
    const [showGrouped, setShowGrouped] = useState(false);

    const handleSearch = (query) => {
        if (query.trim() === '') {
            setIsSearching(false);
            return;
        }

        setIsSearching(true);
        const results = searchProductByName(query, products);
        setFilteredProducts(results);
    };

    const toggleGroupView = () => {
        setShowGrouped(!showGrouped);
    };

    if (error) {
        return (
            <div className="min-h-screen bg-gray-50 flex items-center justify-center">
                <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-md max-w-md">
                    <p className="font-bold">Error loading products</p>
                    <p className="text-sm">{error}</p>
                </div>
            </div>
        );
    }

    const groupedProducts = showGrouped ? groupProductsByCategory(products) : null;

    return (
        <div className="min-h-screen bg-gradient-to-br from-amber-50 via-white to-amber-50">
            {/* Header */}
            <header className="bg-white/80 backdrop-blur-md shadow-lg border-b border-amber-100 sticky top-0 z-40">
                <div className="container mx-auto px-4 py-6">
                    <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4">
                            <button
                                onClick={() => navigate('/')}
                                className="flex items-center space-x-2 group"
                            >
                                <div className="w-10 h-10 bg-gradient-to-br from-amber-600 to-amber-700 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                                    <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z" />
                                    </svg>
                                </div>
                                <h1 className="text-3xl font-bold text-gray-800 group-hover:text-amber-600 transition-colors duration-300">
                                    Coffee <span className="text-amber-600">Right</span>
                                </h1>
                            </button>
                        </div>

                        <div className="flex items-center gap-4">
                            <button
                                onClick={toggleGroupView}
                                className={`px-6 py-3 rounded-full font-medium transition-all duration-300 ${
                                    showGrouped
                                        ? 'bg-amber-600 text-white shadow-lg hover:bg-amber-700 hover:shadow-xl'
                                        : 'bg-white text-amber-600 border-2 border-amber-200 hover:border-amber-400 hover:bg-amber-50'
                                }`}
                            >
                                {showGrouped ? 'Tampilan Normal' : 'Kelompokkan Kategori'}
                            </button>

                            <SearchBar onSearch={handleSearch} />

                            <button
                                onClick={() => navigate('/cart')}
                                className="relative p-3 text-amber-600 hover:text-amber-700 hover:bg-amber-50 rounded-full transition-all duration-300 group"
                            >
                                <svg className="w-6 h-6 group-hover:scale-110 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z" />
                                </svg>
                                {getCartCount() > 0 && (
                                    <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-6 w-6 flex items-center justify-center font-bold animate-pulse">
                                        {getCartCount()}
                                    </span>
                                )}
                            </button>
                        </div>
                    </div>
                </div>
            </header>

            <main className="pt-8">
                <div className="container mx-auto px-4 mb-12">
                    <div className="text-center">
                        <h2 className="text-4xl md:text-5xl font-bold text-gray-800 mb-4">
                            Koleksi <span className="text-amber-600">Kopi Premium</span>
                        </h2>
                        <p className="text-xl text-gray-600 max-w-2xl mx-auto">
                            Temukan berbagai macam kopi berkualitas tinggi dengan cita rasa yang memukau
                        </p>
                    </div>
                </div>

                {isSearching ? (
                    <div className="container mx-auto px-4 py-8">
                        <div className="bg-white rounded-2xl shadow-lg p-8 mb-8">
                            <h2 className="text-3xl font-bold text-gray-800 mb-2">Hasil Pencarian</h2>
                            <p className="text-gray-600 mb-6">Menampilkan hasil untuk pencarian Anda</p>
                        </div>
                        <Products customProducts={filteredProducts} />
                    </div>
                ) : showGrouped ? (
                    <div className="container mx-auto px-4 py-8">
                        <div className="bg-white rounded-2xl shadow-lg p-8 mb-8">
                            <h2 className="text-3xl font-bold text-gray-800 mb-2">Produk Berdasarkan Kategori</h2>
                            <p className="text-gray-600">Jelajahi kopi berdasarkan kategori favorit Anda</p>
                        </div>

                        {Object.entries(groupedProducts).map(([category, categoryProducts]) => (
                            <div key={category} className="mb-12">
                                <div className="bg-gradient-to-r from-amber-600 to-amber-700 rounded-2xl p-6 mb-6 shadow-lg">
                                    <h3 className="text-2xl font-bold text-white capitalize">{category}</h3>
                                    <p className="text-amber-100 mt-2">{categoryProducts.length} produk tersedia</p>
                                </div>

                                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                                    {categoryProducts.map(product => (
                                        <div key={product.id} className="group bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-2xl transition-all duration-300 hover:-translate-y-2">
                                            <div className="relative overflow-hidden">
                                                {product.imageUrl ? (
                                                    <img
                                                        src={product.imageUrl}
                                                        alt={product.name}
                                                        className="w-full h-48 object-cover group-hover:scale-110 transition-transform duration-300"
                                                    />
                                                ) : (
                                                    <div className="w-full h-48 bg-gradient-to-br from-amber-100 to-amber-200 flex items-center justify-center">
                                                        <div className="text-center">
                                                            <svg className="w-16 h-16 text-amber-400 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                                            </svg>
                                                            <p className="text-sm text-amber-600 font-medium">Gambar Segera Hadir</p>
                                                        </div>
                                                    </div>
                                                )}
                                                <div className="absolute top-4 left-4">
                                                    <span className="bg-amber-600 text-white px-3 py-1 rounded-full text-xs font-semibold">
                                                        {category}
                                                    </span>
                                                </div>
                                            </div>

                                            <div className="p-6">
                                                <h3 className="text-xl font-bold text-gray-800 mb-2 group-hover:text-amber-600 transition-colors duration-300">
                                                    {product.name}
                                                </h3>
                                                <p className="text-gray-600 mb-4 line-clamp-2">{product.description}</p>
                                                <div className="flex items-center justify-between">
                                                    <p className="text-2xl font-bold text-amber-600">
                                                        Rp {product.price.toLocaleString()}
                                                    </p>
                                                    <button className="bg-amber-600 hover:bg-amber-700 text-white px-4 py-2 rounded-full text-sm font-semibold transition-all duration-300 hover:scale-105">
                                                        Tambah
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        ))}
                    </div>
                ) : (
                    <div className="container mx-auto px-4">
                        <div className="bg-white rounded-2xl shadow-lg p-8 mb-8">
                            <h2 className="text-3xl font-bold text-gray-800 mb-2">Semua Produk</h2>
                            <p className="text-gray-600">Jelajahi seluruh koleksi kopi premium kami</p>
                        </div>
                        <Products />
                    </div>
                )}
            </main>
        </div>
    );
};

export default ProductsPage;


