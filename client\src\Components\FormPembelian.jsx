import React, { useState, useContext } from "react";
import { CartContext } from "../Context/CartContext";
import { OrderContext } from "../Context/OrderContext";
import { useNavigate } from "react-router-dom";

const FormPembelian = () => {
    const [name, setName] = useState('')
    const [alamat, setAlamat] = useState('')
    const [nomorHp, setNomorHp] = useState('')
    const [paymentMethod, setPaymentMethod] = useState('cod')
    const [isSubmitting, setIsSubmitting] = useState(false)

    const { cartItems, getCartTotal, clearCart } = useContext(CartContext);
    const { createOrder } = useContext(OrderContext);
    const navigate = useNavigate();

    const handleFormSubmit = async (e) => {
        e.preventDefault()
        if (!name || !alamat || !nomorHp) {
            alert('<PERSON>hon lengkapi semua field')
            return;
        }

        if (cartItems.length === 0) {
            alert('Keranjang belanja kosong')
            return;
        }

        setIsSubmitting(true);

        try {
            const orderData = {
                customerInfo: {
                    name,
                    alamat,
                    nomorHp
                },
                items: cartItems,
                total: getCartTotal(),
                paymentMethod,
                orderNumber: `ORD-${Date.now()}`
            };

            const newOrder = await createOrder(orderData);
            clearCart();
            alert('Pesanan berhasil dibuat!');
            navigate(`/order-tracking/${newOrder.id}`);
        } catch (error) {
            console.error('Error creating order:', error);
            alert('Gagal membuat pesanan. Silakan coba lagi.');
        } finally {
            setIsSubmitting(false);
        }
    }
    return (
        <div className="space-y-6">
            {/* Order Summary */}
            <div className="bg-white p-6 rounded-lg shadow-md">
                <h2 className="text-xl font-bold text-gray-800 mb-4">Detail Pesanan</h2>
                {cartItems.length === 0 ? (
                    <p className="text-gray-500">Keranjang belanja kosong</p>
                ) : (
                    <div className="space-y-3">
                        {cartItems.map((item) => (
                            <div key={item.id} className="flex justify-between items-center border-b pb-2">
                                <div className="flex-1">
                                    <h3 className="font-medium text-gray-800">{item.name}</h3>
                                    <p className="text-sm text-gray-600">Qty: {item.quantity}</p>
                                </div>
                                <div className="text-right">
                                    <p className="font-medium text-gray-800">
                                        Rp {(item.price * item.quantity).toLocaleString('id-ID')}
                                    </p>
                                </div>
                            </div>
                        ))}
                        <div className="border-t pt-3">
                            <div className="flex justify-between items-center font-bold text-lg">
                                <span>Total:</span>
                                <span className="text-blue-600">
                                    Rp {getCartTotal().toLocaleString('id-ID')}
                                </span>
                            </div>
                        </div>
                    </div>
                )}
            </div>

            {/* Customer Information Form */}
            <form onSubmit={handleFormSubmit} className="bg-white p-6 rounded-lg shadow-md">
                <h2 className="text-xl font-bold text-gray-800 mb-4">Informasi Pengiriman</h2>

                <div className="mb-4">
                    <label htmlFor="name" className="block text-gray-700 font-medium mb-2">Nama Lengkap</label>
                    <input
                        type="text"
                        id="name"
                        value={name}
                        onChange={(e) => setName(e.target.value)}
                        className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-600 focus:border-transparent"
                        required
                    />
                </div>

                <div className="mb-4">
                    <label htmlFor="alamat" className="block text-gray-700 font-medium mb-2">Alamat Lengkap</label>
                    <textarea
                        id="alamat"
                        value={alamat}
                        onChange={(e) => setAlamat(e.target.value)}
                        rows="3"
                        className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-600 focus:border-transparent"
                        required
                    />
                </div>

                <div className="mb-4">
                    <label htmlFor="nomorHp" className="block text-gray-700 font-medium mb-2">Nomor HP</label>
                    <input
                        type="tel"
                        id="nomorHp"
                        value={nomorHp}
                        onChange={(e) => setNomorHp(e.target.value)}
                        className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-600 focus:border-transparent"
                        required
                    />
                </div>

                <div className="mb-6">
                    <label className="block text-gray-700 font-medium mb-2">Metode Pembayaran</label>
                    <div className="space-y-2">
                        <label className="flex items-center">
                            <input
                                type="radio"
                                name="paymentMethod"
                                value="cod"
                                checked={paymentMethod === 'cod'}
                                onChange={(e) => setPaymentMethod(e.target.value)}
                                className="mr-2"
                            />
                            <span>Cash on Delivery (COD)</span>
                        </label>
                        <label className="flex items-center">
                            <input
                                type="radio"
                                name="paymentMethod"
                                value="transfer"
                                checked={paymentMethod === 'transfer'}
                                onChange={(e) => setPaymentMethod(e.target.value)}
                                className="mr-2"
                            />
                            <span>Transfer Bank</span>
                        </label>
                    </div>
                </div>

                <button
                    type="submit"
                    disabled={isSubmitting || cartItems.length === 0}
                    className={`w-full py-3 px-4 rounded-md transition-colors duration-200 font-medium ${
                        isSubmitting || cartItems.length === 0
                            ? 'bg-gray-400 cursor-not-allowed'
                            : 'bg-blue-600 hover:bg-blue-700 text-white'
                    }`}
                >
                    {isSubmitting ? 'Memproses...' : 'Buat Pesanan'}
                </button>
            </form>
        </div>
    )
}

export default FormPembelian;
