import React, { useState } from "react";

const FormPembelian = () => {
    const [name, setName] = useState('')
    const [alamat, setAlamat] = useState('')
    const [nomorHp, setNomorHp] = useState('')
    const handleFormSubmit = (e) => {
        e.PreventDefault()
        if (!name || !alamat || !nomorHp) {
            alert('Please fill in all fields')
        }
    }
    return (
        <>
            <form onSubmit={handleFormSubmit} className="bg-white p-6 rounded-lg shadow-md max-w-md mx-auto">
                <div className="mb-4">
                    <label htmlFor="name" className="block text-gray-700 font-medium mb-2">Name</label>
                    <input
                        type="text"
                        id="name"
                        value={name}
                        onChange={(e) => setName(e.target.value)}
                        className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-600 focus:border-transparent"
                    />
                </div>
                <div className="mb-4">
                    <label htmlFor="alamat" className="block text-gray-700 font-medium mb-2">Alamat</label>
                    <input
                        type="text"
                        id="alamat"
                        value={alamat}
                        onChange={(e) => setAlamat(e.target.value)}
                        className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-600 focus:border-transparent"
                    />
                </div>
                <div className="mb-4">
                    <label htmlFor="nomorHp" className="block text-gray-700 font-medium mb-2">Nomor Hp</label>
                    <input
                        type="text"
                        id="nomorHp"
                        value={nomorHp}
                        onChange={(e) => setNomorHp(e.target.value)}
                        className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-600 focus:border-transparent"
                    />
                </div>
                <button
                    type="submit"
                    className="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md transition-colors duration-200"
                >
                    Submit
                </button>
            </form>
        </>
    )
}

export default FormPembelian;
