import React, { useContext } from 'react';
import { OrderContext } from '../Context/OrderContext';

const OrderStatus = ({ currentStatus }) => {
  const { ORDER_STATUSES, getStatusInfo } = useContext(OrderContext);

  const statusOrder = [
    ORDER_STATUSES.ORDERED,
    ORDER_STATUSES.PAID,
    ORDER_STATUSES.PROCESSING,
    ORDER_STATUSES.READY,
    ORDER_STATUSES.DELIVERING,
    ORDER_STATUSES.COMPLETED
  ];

  const currentIndex = statusOrder.indexOf(currentStatus);

  const getStepIcon = (status, index) => {
    const isCompleted = index <= currentIndex;
    const isCurrent = index === currentIndex;
    
    const icons = {
      [ORDER_STATUSES.ORDERED]: '📋',
      [ORDER_STATUSES.PAID]: '💳',
      [ORDER_STATUSES.PROCESSING]: '👨‍🍳',
      [ORDER_STATUSES.READY]: '✅',
      [ORDER_STATUSES.DELIVERING]: '🚗',
      [ORDER_STATUSES.COMPLETED]: '🎉'
    };

    return (
      <div className={`flex items-center justify-center w-12 h-12 rounded-full border-2 transition-all duration-300 ${
        isCompleted 
          ? 'bg-green-500 border-green-500 text-white' 
          : isCurrent
          ? 'bg-blue-500 border-blue-500 text-white animate-pulse'
          : 'bg-gray-200 border-gray-300 text-gray-500'
      }`}>
        <span className="text-lg">{icons[status]}</span>
      </div>
    );
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow-md">
      <h2 className="text-xl font-bold text-gray-800 mb-6">Status Pesanan</h2>
      
      <div className="relative">
        {/* Progress Line */}
        <div className="absolute left-6 top-6 h-full w-0.5 bg-gray-200">
          <div 
            className="bg-green-500 w-full transition-all duration-500 ease-in-out"
            style={{ height: `${(currentIndex / (statusOrder.length - 1)) * 100}%` }}
          />
        </div>

        {/* Status Steps */}
        <div className="space-y-8">
          {statusOrder.map((status, index) => {
            const statusInfo = getStatusInfo(status);
            const isCompleted = index <= currentIndex;
            const isCurrent = index === currentIndex;

            return (
              <div key={status} className="relative flex items-start">
                {/* Icon */}
                <div className="relative z-10">
                  {getStepIcon(status, index)}
                </div>

                {/* Content */}
                <div className="ml-4 flex-1">
                  <div className={`font-medium ${
                    isCompleted ? 'text-green-600' : 
                    isCurrent ? 'text-blue-600' : 'text-gray-400'
                  }`}>
                    {statusInfo.label}
                  </div>
                  <div className={`text-sm mt-1 ${
                    isCompleted || isCurrent ? 'text-gray-600' : 'text-gray-400'
                  }`}>
                    {statusInfo.description}
                  </div>
                  {isCurrent && (
                    <div className="mt-2">
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        Sedang Berlangsung
                      </span>
                    </div>
                  )}
                </div>

                {/* Timestamp (if completed) */}
                {isCompleted && (
                  <div className="text-xs text-gray-500">
                    {new Date().toLocaleTimeString('id-ID', { 
                      hour: '2-digit', 
                      minute: '2-digit' 
                    })}
                  </div>
                )}
              </div>
            );
          })}
        </div>
      </div>

      {/* Current Status Card */}
      <div className={`mt-6 p-4 rounded-lg ${getStatusInfo(currentStatus).color} bg-opacity-10`}>
        <div className="flex items-center">
          <div className={`w-3 h-3 rounded-full ${getStatusInfo(currentStatus).color} mr-3`}></div>
          <div>
            <div className="font-medium text-gray-800">
              Status Saat Ini: {getStatusInfo(currentStatus).label}
            </div>
            <div className="text-sm text-gray-600 mt-1">
              {getStatusInfo(currentStatus).description}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OrderStatus;
