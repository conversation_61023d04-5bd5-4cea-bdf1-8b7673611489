import React, { useState } from "react";

const SearchBar = ({ onSearch }) => {
    const [searchTerm, setSearchTerm] = useState('')
    const handleSearchChange = (e) => {
        setSearchTerm(e.target.value)
    }

    const handlerSubmit = (e) => {
        e.preventDefault();
        console.log("Searching for:", searchTerm);
        if (typeof onSearch === 'function') {
            onSearch(searchTerm);
        } else {
            console.error("onSearch is not a function:", onSearch);
        }
    }

    return (
        <div className="relative">
            <form onSubmit={handlerSubmit} className="flex items-center">
                <div className="relative">
                    <input
                        type="text"
                        placeholder="Cari kopi favorit..."
                        className="w-64 pl-12 pr-4 py-3 bg-white border-2 border-amber-200 rounded-full focus:outline-none focus:border-amber-400 focus:ring-4 focus:ring-amber-100 transition-all duration-300 text-gray-700 placeholder-gray-400"
                        value={searchTerm}
                        onChange={handleSearchChange}
                    />
                    <div className="absolute left-4 top-1/2 transform -translate-y-1/2">
                        <svg className="w-5 h-5 text-amber-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                        </svg>
                    </div>
                </div>

                <button
                    type="submit"
                    className="ml-3 px-6 py-3 bg-amber-600 hover:bg-amber-700 text-white rounded-full font-medium transition-all duration-300 hover:scale-105 hover:shadow-lg flex items-center space-x-2"
                >
                    <span>Cari</span>
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                    </svg>
                </button>
            </form>
        </div>
    );
}

export default SearchBar
