import React, { useContext } from 'react';
import { OrderContext } from '../Context/OrderContext';

const AdminOrderPanel = ({ orderId, currentStatus }) => {
  const { updateOrderStatus, ORDER_STATUSES, getStatusInfo } = useContext(OrderContext);

  const statusOrder = [
    ORDER_STATUSES.ORDERED,
    ORDER_STATUSES.PAID,
    ORDER_STATUSES.PROCESSING,
    ORDER_STATUSES.READY,
    ORDER_STATUSES.DELIVERING,
    ORDER_STATUSES.COMPLETED
  ];

  const handleStatusUpdate = async (newStatus) => {
    try {
      await updateOrderStatus(orderId, newStatus);
      alert(`Status berhasil diperbarui ke: ${getStatusInfo(newStatus).label}`);
    } catch (error) {
      alert('Gagal memperbarui status');
      console.error('Error updating status:', error);
    }
  };

  return (
    <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 mt-4">
      <h3 className="font-medium text-gray-800 mb-3">🔧 Panel Admin (Demo)</h3>
      <p className="text-sm text-gray-600 mb-4">
        Gunakan tombol di bawah untuk mensimulasikan perubahan status pesanan:
      </p>
      
      <div className="grid grid-cols-2 gap-2">
        {statusOrder.map((status) => {
          const statusInfo = getStatusInfo(status);
          const isCurrentStatus = status === currentStatus;
          
          return (
            <button
              key={status}
              onClick={() => handleStatusUpdate(status)}
              disabled={isCurrentStatus}
              className={`p-2 rounded text-sm font-medium transition-colors duration-200 ${
                isCurrentStatus
                  ? 'bg-blue-100 text-blue-800 cursor-not-allowed'
                  : 'bg-white hover:bg-gray-100 text-gray-700 border border-gray-300'
              }`}
            >
              {statusInfo.label}
              {isCurrentStatus && ' (Aktif)'}
            </button>
          );
        })}
      </div>
      
      <p className="text-xs text-gray-500 mt-3 text-center">
        *Panel ini hanya untuk demo. Dalam aplikasi nyata, status akan diperbarui oleh sistem backend.
      </p>
    </div>
  );
};

export default AdminOrderPanel;
