
import { useCart } from '../Context/useCart';

const ProductItem = ({ product }) => {
    const { addToCart } = useCart();
    const imageUrl = product.imageUrl || product.imageURL || product.image_url || product.image;

    const handleAddToCart = () => {
        addToCart(product);

        const notification = document.createElement('div');
        notification.className = 'fixed top-4 right-4 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg z-50 transform translate-x-full transition-transform duration-300';
        notification.textContent = `${product.name} ditambahkan ke keranjang!`;
        document.body.appendChild(notification);

        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 100);

        setTimeout(() => {
            notification.style.transform = 'translateX(full)';
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 2000);
    };

    return (
        <div className="group bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-2xl transition-all duration-300 hover:-translate-y-2">
            <div className="relative h-56 w-full overflow-hidden">
                {imageUrl && imageUrl.trim() !== '' ? (
                    <>
                        <img
                            src={imageUrl}
                            alt={product.name || 'Product image'}
                            className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                            onLoad={() => {
                                console.log('Image loaded successfully:', imageUrl);
                            }}
                            onError={(e) => {
                                console.error('Image failed to load:', imageUrl);
                                console.error('Error event:', e);
                                e.target.style.display = 'none';
                                const placeholder = e.target.parentElement.querySelector('.image-placeholder');
                                if (placeholder) {
                                    placeholder.style.display = 'flex';
                                }
                            }}
                        />
                        <div className="image-placeholder w-full h-full bg-gradient-to-br from-amber-100 to-amber-200 flex items-center justify-center absolute top-0 left-0" style={{display: 'none'}}>
                            <div className="text-center">
                                <svg className="w-16 h-16 text-amber-400 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                </svg>
                                <p className="text-sm text-amber-600 font-medium">Gambar Segera Hadir</p>
                            </div>
                        </div>
                    </>
                ) : (
                    <div className="w-full h-full bg-gradient-to-br from-amber-100 to-amber-200 flex items-center justify-center">
                        <div className="text-center">
                            <svg className="w-16 h-16 text-amber-400 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                            </svg>
                            <p className="text-sm text-amber-600 font-medium">Gambar Segera Hadir</p>
                        </div>
                    </div>
                )}


                <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

                {product.category && (
                    <div className="absolute top-4 left-4">
                        <span className="bg-amber-600 text-white text-xs px-3 py-1 rounded-full font-semibold shadow-lg">
                            {product.category}
                        </span>
                    </div>
                )}


                <div className="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <button className="bg-white/80 hover:bg-white text-amber-600 p-2 rounded-full shadow-lg hover:scale-110 transition-all duration-300">
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                        </svg>
                    </button>
                </div>
            </div>


            <div className="p-6">
                <div className="mb-3">
                    <h3 className="text-xl font-bold text-gray-800 mb-1 group-hover:text-amber-600 transition-colors duration-300">
                        {product.name}
                    </h3>
                    {product.id && (
                        <span className="text-xs text-gray-400 font-mono">
                            #{product.id}
                        </span>
                    )}
                </div>

                <p className="text-gray-600 text-sm mb-6 line-clamp-2 leading-relaxed">
                    {product.description}
                </p>

                <div className="flex items-center justify-between">
                    <div className="flex flex-col">
                        <span className="text-2xl font-bold text-amber-600">
                            Rp {product.price?.toLocaleString('id-ID')}
                        </span>
                        <span className="text-xs text-gray-500">per porsi</span>
                    </div>

                    <button
                        onClick={handleAddToCart}
                        className="group/btn bg-amber-600 hover:bg-amber-700 text-white px-6 py-3 rounded-full font-semibold transition-all duration-300 hover:scale-105 hover:shadow-lg flex items-center space-x-2"
                    >
                        <span>Tambah</span>
                        <svg className="w-4 h-4 group-hover/btn:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                        </svg>
                    </button>
                </div>

                <div className="flex items-center mt-4 pt-4 border-t border-gray-100">
                    <div className="flex items-center space-x-1">
                        {[...Array(5)].map((_, i) => (
                            <svg key={i} className="w-4 h-4 text-amber-400 fill-current" viewBox="0 0 24 24">
                                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" />
                            </svg>
                        ))}
                    </div>
                    <span className="text-xs text-gray-500 ml-2">(4.8)</span>
                </div>
            </div>
        </div>
    );
};

export default ProductItem;

