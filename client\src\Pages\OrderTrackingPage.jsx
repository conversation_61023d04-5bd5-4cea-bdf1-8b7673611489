import React, { useContext, useEffect, useState } from 'react';
import { useParams, Link } from 'react-router-dom';
import { OrderContext } from '../Context/OrderContext';
import OrderStatus from '../Components/OrderStatus';
import OrderDetails from '../Components/OrderDetails';
import AdminOrderPanel from '../Components/AdminOrderPanel';

const OrderTrackingPage = () => {
  const { orderId } = useParams();
  const { getOrderById, updateOrderStatus, ORDER_STATUSES } = useContext(OrderContext);
  const [order, setOrder] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchOrder = () => {
      const foundOrder = getOrderById(orderId);
      setOrder(foundOrder);
      setLoading(false);
    };

    fetchOrder();
  }, [orderId, getOrderById]);

  const simulateStatusUpdate = async () => {
    if (!order) return;

    const statusOrder = [
      ORDER_STATUSES.ORDERED,
      ORDER_STATUSES.PAID,
      ORDER_STATUSES.PROCESSING,
      ORDER_STATUSES.READY,
      ORDER_STATUSES.DELIVERING,
      ORDER_STATUSES.COMPLETED
    ];

    const currentIndex = statusOrder.indexOf(order.status);
    if (currentIndex < statusOrder.length - 1) {
      const nextStatus = statusOrder[currentIndex + 1];
      try {
        await updateOrderStatus(orderId, nextStatus);
        setOrder(prev => ({ ...prev, status: nextStatus }));
      } catch (error) {
        console.error('Error updating status:', error);
      }
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-amber-50 via-orange-50 to-yellow-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Memuat detail pesanan...</p>
        </div>
      </div>
    );
  }

  if (!order) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-amber-50 via-orange-50 to-yellow-100 flex items-center justify-center">
        <div className="text-center">
          <div className="text-6xl mb-4">😕</div>
          <h1 className="text-2xl font-bold text-gray-800 mb-2">Pesanan Tidak Ditemukan</h1>
          <p className="text-gray-600 mb-6">Pesanan dengan ID tersebut tidak dapat ditemukan.</p>
          <Link
            to="/products"
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md transition-colors duration-200"
          >
            Kembali Berbelanja
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-amber-50 via-orange-50 to-yellow-100 py-8 px-4 sm:px-6 lg:px-8">
      <div className="max-w-4xl mx-auto">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Lacak Pesanan</h1>
          <p className="text-gray-600">Pantau status pesanan Anda secara real-time</p>
        </div>

        <div className="mb-6">
          <Link
            to="/products"
            className="inline-flex items-center text-blue-600 hover:text-blue-800 transition-colors duration-200"
          >
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
            Kembali ke Produk
          </Link>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">

          <div>
            <OrderStatus currentStatus={order.status} />


            <AdminOrderPanel orderId={orderId} currentStatus={order.status} />


            {order.status !== ORDER_STATUSES.COMPLETED && (
              <div className="mt-4">
                <button
                  onClick={simulateStatusUpdate}
                  className="w-full bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded-md transition-colors duration-200 text-sm"
                >
                  🧪 Demo: Update Status Berikutnya
                </button>
                <p className="text-xs text-gray-500 text-center mt-2">
                  *Tombol ini hanya untuk demo. Dalam aplikasi nyata, status akan diperbarui otomatis.
                </p>
              </div>
            )}
          </div>


          <div>
            <OrderDetails order={order} />
          </div>
        </div>

        {/* Success Message for Completed Orders */}
        {order.status === ORDER_STATUSES.COMPLETED && (
          <div className="mt-8 bg-green-50 border border-green-200 rounded-lg p-6 text-center">
            <div className="text-4xl mb-4">🎉</div>
            <h2 className="text-xl font-bold text-green-800 mb-2">Pesanan Selesai!</h2>
            <p className="text-green-700 mb-4">
              Terima kasih telah berbelanja dengan kami. Pesanan Anda telah berhasil diantar.
            </p>
            <div className="space-x-4">
              <Link
                to="/products"
                className="bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-md transition-colors duration-200"
              >
                Pesan Lagi
              </Link>
              <button className="bg-gray-200 hover:bg-gray-300 text-gray-800 px-6 py-2 rounded-md transition-colors duration-200">
                Beri Rating
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default OrderTrackingPage;
