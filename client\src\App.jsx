import './App.css'
import LoginPages from './Pages/LoginPages'
import SignUpPage from './Pages/signUpPage';
import HomePage from './Pages/HomePage'
import ProductsPage from './Pages/ProductsPage'
import CartPage from './Pages/CartPage'
import FormPembelianPage from './Pages/FormPembelianPage'
import OrderTrackingPage from './Pages/OrderTrackingPage'
import { Routes, Route } from "react-router-dom";
import { CartProvider } from './Context/CartContext';
import { OrderProvider } from './Context/OrderContext';

function App() {
  return (
    <CartProvider>
      <OrderProvider>
        <Routes>
          <Route path="/" element={<HomePage />} />
          <Route path="/login" element={<LoginPages />} />
          <Route path="/signup" element={<SignUpPage />} />
          <Route path="/products" element={<ProductsPage />} />
          <Route path="/cart" element={<CartPage />} />
          <Route path="/form-pembelian" element={<FormPembelianPage />} />
          <Route path="/order-tracking/:orderId" element={<OrderTrackingPage />} />
        </Routes>
      </OrderProvider>
    </CartProvider>
  )
}

export default App


