import './App.css'
import LoginPages from './Pages/LoginPages'
import SignUpPage from './Pages/signUpPage';
import HomePage from './Pages/HomePage'
import ProductsPage from './Pages/ProductsPage'
import CartPage from './Pages/CartPage'
import FormPembelianPage from './Pages/FormPembelianPage'
import { Routes, Route } from "react-router-dom";
import { CartProvider } from './Context/CartContext';

function App() {
  return (
    <CartProvider>
      <Routes>
        <Route path="/" element={<HomePage />} />
        <Route path="/login" element={<LoginPages />} />
        <Route path="/signup" element={<SignUpPage />} />
        <Route path="/products" element={<ProductsPage />} />
        <Route path="/cart" element={<CartPage />} />
        <Route path="/form-pembelian" element={<FormPembelianPage />} />
      </Routes>
    </CartProvider>
  )
}

export default App


