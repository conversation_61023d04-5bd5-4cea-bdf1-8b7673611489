import React from 'react';

const OrderDetails = ({ order }) => {
  if (!order) {
    return (
      <div className="bg-white p-6 rounded-lg shadow-md">
        <div className="text-center text-gray-500">
          <p>Order tidak ditemukan</p>
        </div>
      </div>
    );
  }

  const formatDate = (date) => {
    return new Date(date.seconds ? date.seconds * 1000 : date).toLocaleString('id-ID', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className="space-y-6">
      {/* Order Header */}
      <div className="bg-white p-6 rounded-lg shadow-md">
        <div className="flex justify-between items-start mb-4">
          <div>
            <h2 className="text-xl font-bold text-gray-800">Detail Pesanan</h2>
            <p className="text-gray-600">#{order.orderNumber}</p>
          </div>
          <div className="text-right">
            <p className="text-sm text-gray-500"><PERSON>gal Pesanan</p>
            <p className="font-medium">{formatDate(order.createdAt)}</p>
          </div>
        </div>

        {/* Customer Info */}
        <div className="border-t pt-4">
          <h3 className="font-medium text-gray-800 mb-3">Informasi Pelanggan</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <p className="text-sm text-gray-500">Nama</p>
              <p className="font-medium">{order.customerInfo.name}</p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Nomor HP</p>
              <p className="font-medium">{order.customerInfo.nomorHp}</p>
            </div>
            <div className="md:col-span-2">
              <p className="text-sm text-gray-500">Alamat</p>
              <p className="font-medium">{order.customerInfo.alamat}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Order Items */}
      <div className="bg-white p-6 rounded-lg shadow-md">
        <h3 className="font-medium text-gray-800 mb-4">Item Pesanan</h3>
        <div className="space-y-3">
          {order.items.map((item) => (
            <div key={item.id} className="flex justify-between items-center border-b pb-3 last:border-b-0">
              <div className="flex-1">
                <h4 className="font-medium text-gray-800">{item.name}</h4>
                <p className="text-sm text-gray-600">{item.description}</p>
                <p className="text-sm text-gray-500">
                  Rp {item.price.toLocaleString('id-ID')} x {item.quantity}
                </p>
              </div>
              <div className="text-right">
                <p className="font-medium text-gray-800">
                  Rp {(item.price * item.quantity).toLocaleString('id-ID')}
                </p>
              </div>
            </div>
          ))}
        </div>

        {/* Order Summary */}
        <div className="border-t pt-4 mt-4">
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">Subtotal</span>
              <span>Rp {order.total.toLocaleString('id-ID')}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">Ongkos Kirim</span>
              <span>Gratis</span>
            </div>
            <div className="flex justify-between font-bold text-lg border-t pt-2">
              <span>Total</span>
              <span className="text-blue-600">Rp {order.total.toLocaleString('id-ID')}</span>
            </div>
          </div>
        </div>

        {/* Payment Method */}
        <div className="border-t pt-4 mt-4">
          <div className="flex justify-between items-center">
            <span className="text-gray-600">Metode Pembayaran</span>
            <span className="font-medium">
              {order.paymentMethod === 'cod' ? 'Cash on Delivery (COD)' : 'Transfer Bank'}
            </span>
          </div>
        </div>
      </div>

      {/* Admin Actions (for testing status updates) */}
      <div className="bg-gray-50 p-4 rounded-lg">
        <p className="text-xs text-gray-500 text-center">
          Status pesanan akan diperbarui secara otomatis oleh sistem
        </p>
      </div>
    </div>
  );
};

export default OrderDetails;
