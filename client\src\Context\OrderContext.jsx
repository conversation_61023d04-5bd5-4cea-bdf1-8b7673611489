import React, { createContext, useState, useEffect } from 'react';
import { collection, addDoc, doc, updateDoc, getDocs, query, orderBy } from 'firebase/firestore';
import { db } from '../Config/firebase';

const OrderContext = createContext();

export { OrderContext };

export const OrderProvider = ({ children }) => {
  const [orders, setOrders] = useState([]);
  const [loading, setLoading] = useState(false);

  // Order statuses
  const ORDER_STATUSES = {
    ORDERED: 'ordered',
    PAID: 'paid', 
    PROCESSING: 'processing',
    READY: 'ready',
    DELIVERING: 'delivering',
    COMPLETED: 'completed'
  };

  // Fetch orders from Firestore
  const fetchOrders = async () => {
    try {
      setLoading(true);
      const q = query(collection(db, "orders"), orderBy("createdAt", "desc"));
      const querySnapshot = await getDocs(q);
      const ordersData = [];

      querySnapshot.forEach((doc) => {
        ordersData.push({
          id: doc.id,
          ...doc.data()
        });
      });

      setOrders(ordersData);
    } catch (error) {
      console.error('Error fetching orders:', error);
    } finally {
      setLoading(false);
    }
  };

  // Create new order
  const createOrder = async (orderData) => {
    try {
      setLoading(true);
      const newOrder = {
        ...orderData,
        status: ORDER_STATUSES.ORDERED,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      const docRef = await addDoc(collection(db, "orders"), newOrder);
      const orderWithId = { id: docRef.id, ...newOrder };
      
      setOrders(prevOrders => [orderWithId, ...prevOrders]);
      return orderWithId;
    } catch (error) {
      console.error('Error creating order:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Update order status
  const updateOrderStatus = async (orderId, newStatus) => {
    try {
      const orderRef = doc(db, "orders", orderId);
      await updateDoc(orderRef, {
        status: newStatus,
        updatedAt: new Date()
      });

      setOrders(prevOrders =>
        prevOrders.map(order =>
          order.id === orderId
            ? { ...order, status: newStatus, updatedAt: new Date() }
            : order
        )
      );
    } catch (error) {
      console.error('Error updating order status:', error);
      throw error;
    }
  };

  // Get order by ID
  const getOrderById = (orderId) => {
    return orders.find(order => order.id === orderId);
  };

  // Get status display info
  const getStatusInfo = (status) => {
    const statusConfig = {
      [ORDER_STATUSES.ORDERED]: {
        label: 'Pesanan Diterima',
        color: 'bg-blue-500',
        description: 'Pesanan Anda telah diterima dan sedang diverifikasi'
      },
      [ORDER_STATUSES.PAID]: {
        label: 'Pembayaran Berhasil',
        color: 'bg-green-500',
        description: 'Pembayaran telah dikonfirmasi'
      },
      [ORDER_STATUSES.PROCESSING]: {
        label: 'Sedang Diproses',
        color: 'bg-yellow-500',
        description: 'Makanan sedang disiapkan oleh chef'
      },
      [ORDER_STATUSES.READY]: {
        label: 'Siap Diantar',
        color: 'bg-purple-500',
        description: 'Makanan sudah siap dan akan segera diantar'
      },
      [ORDER_STATUSES.DELIVERING]: {
        label: 'Sedang Diantar',
        color: 'bg-orange-500',
        description: 'Kurir sedang dalam perjalanan menuju lokasi Anda'
      },
      [ORDER_STATUSES.COMPLETED]: {
        label: 'Selesai',
        color: 'bg-green-600',
        description: 'Pesanan telah selesai diantar'
      }
    };

    return statusConfig[status] || {
      label: 'Unknown',
      color: 'bg-gray-500',
      description: 'Status tidak diketahui'
    };
  };

  useEffect(() => {
    fetchOrders();
  }, []);

  const value = {
    orders,
    loading,
    ORDER_STATUSES,
    createOrder,
    updateOrderStatus,
    getOrderById,
    getStatusInfo,
    fetchOrders
  };

  return <OrderContext.Provider value={value}>{children}</OrderContext.Provider>;
};
